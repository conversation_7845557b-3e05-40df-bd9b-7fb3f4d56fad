"use client"
import { useState, useRef, useEffect } from 'react';
import { Globe, SquareArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function SidebarNavigation() {
    const [isOpen, setIsOpen] = useState(false);
    const sidebarRef = useRef<HTMLDivElement>(null);

    const toggleSidebar = () => setIsOpen(true);
    const closeSidebar = () => setIsOpen(false);

    const router = useRouter();

    const startNewChat = () => {
        router.push('#', { scroll: false }); // change to your actual route
    };


    // Close on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                sidebarRef.current &&
                !sidebarRef.current.contains(event.target as Node)
            ) {
                closeSidebar();
            }
        }

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    return (
        <div className="relative h-screen">
            {/* Sidebar */}
            <div
                ref={sidebarRef}
                className={`fixed top-0 left-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 ${isOpen ? 'translate-x-0' : '-translate-x-full'
                    }`}
            >
                <div className="p-4">
                    <h1 className="text-xl font-bold mb-4">Navigation</h1>
                    <button onClick={closeSidebar}>
                        <SquareArrowRight className="w-6 h-6 ml-45 -mt-15 cursor-pointer" />
                    </button>
                    <Button variant="default" className="w-full cursor-pointer" onClick={startNewChat}>
                        + New Chat
                    </Button>
                </div>
            </div>

            {/* Toggle Button */}
            <button
                onClick={toggleSidebar}
                className={`absolute top-4 left-4 z-60 bg-white p-2 rounded-full drop-shadow-lg shadow-black transition-opacity duration-300 ${isOpen ? 'invisible pointer-events-none' : ''
                    }`}
            >
                <Globe className="w-6 h-6 animate-spin cursor-pointer" />
            </button>
        </div>
    );
}
