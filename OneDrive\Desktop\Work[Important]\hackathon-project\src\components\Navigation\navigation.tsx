"use client"
import { useState, useRef, useEffect } from 'react';
import { Globe, SquareArrowRight, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';


interface Message {
  text: string;
  isAi: boolean;
}

interface ChatHistory {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
}

interface NavigationProps {
  onNewChat: () => void;
  onLoadChat: (chat: ChatHistory) => void;
  chatHistory: ChatHistory[];
  currentChatId: string | null;
}

export default function SidebarNavigation({
  onNewChat,
  onLoadChat,
  chatHistory,
  currentChatId
}: NavigationProps) {
    const [isOpen, setIsOpen] = useState(false);
    const sidebarRef = useRef<HTMLDivElement>(null);

    const toggleSidebar = () => setIsOpen(true);
    const closeSidebar = () => setIsOpen(false);

    const startNewChat = () => {
        onNewChat();
        closeSidebar();
    };

    const formatTimestamp = (timestamp: Date) => {
        const now = new Date();
        const diff = now.getTime() - timestamp.getTime();
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days === 0) {
            return 'Today';
        } else if (days === 1) {
            return 'Yesterday';
        } else if (days < 7) {
            return `${days} days ago`;
        } else {
            return timestamp.toLocaleDateString();
        }
    };


    // Close on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                sidebarRef.current &&
                !sidebarRef.current.contains(event.target as Node)
            ) {
                closeSidebar();
            }
        }

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    return (
        <div className="relative h-screen">
            {/* Sidebar */}
            <div
                ref={sidebarRef}
                className={`fixed top-0 left-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 ${isOpen ? 'translate-x-0' : '-translate-x-full'
                    }`}
            >
                <div className="p-4 h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                        <h1 className="text-xl font-bold">Navigation</h1>
                        <button onClick={closeSidebar}>
                            <SquareArrowRight className="w-6 h-6 cursor-pointer" />
                        </button>
                    </div>

                    <Button variant="default" className="w-full cursor-pointer mb-4" onClick={startNewChat}>
                        + New Chat
                    </Button>

                    {/* Chat History */}
                    <div className="flex-1 overflow-y-auto">
                        <h2 className="text-sm font-semibold text-gray-600 mb-2">Previous Chats</h2>
                        <div className="space-y-2">
                            {chatHistory.map((chat) => (
                                <div
                                    key={chat.id}
                                    onClick={() => {
                                        onLoadChat(chat);
                                        closeSidebar();
                                    }}
                                    className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-100 ${
                                        currentChatId === chat.id ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                                    }`}
                                >
                                    <div className="flex items-start gap-2">
                                        <MessageSquare className="w-4 h-4 mt-0.5 text-gray-500 flex-shrink-0" />
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {chat.title}
                                            </p>
                                            <p className="text-xs text-gray-500 mt-1">
                                                {formatTimestamp(chat.timestamp)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                            {chatHistory.length === 0 && (
                                <p className="text-sm text-gray-500 text-center py-4">
                                    No previous chats
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Toggle Button */}
            <button
                onClick={toggleSidebar}
                className={`absolute top-4 left-4 z-60 bg-white p-2 rounded-full drop-shadow-lg shadow-black transition-opacity duration-300 ${isOpen ? 'invisible pointer-events-none' : ''
                    }`}
            >
                <Globe className="w-6 h-6 animate-spin cursor-pointer" />
            </button>
        </div>
    );
}
