# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Groq API Configuration
GROQ_API_KEY=ok now that the frontend is done make me the backend in express for the ai using groqapi it will a insurence ai, where first we have to upload the insaurence paper {it can be of any format} and then the ai will answer us the questions based on that papers. the ai should fully comprehend the papers{50-100 pages] and remerber all it's content. also the moment the papers are uploaded it will give a summary in detail covering all important points.

also fully integrate it so the Aiinput also works with this.


import { Groq } from 'groq-sdk';

const groq = new Groq();

const chatCompletion = await groq.chat.completions.create({
  "messages": [
    {
      "role": "user",
      "content": ""
    }
  ],
  "model": "compound-beta",
  "temperature": 1,
  "max_completion_tokens": 1024,
  "top_p": 1,
  "stream": true,
  "stop": null
});

for await (const chunk of chatCompletion) {
  process.stdout.write(chunk.choices[0]?.delta?.content || '');
}

make it so it can also generate public api so others can use it, and add api button on the top right of the website with a arrow, when clicked it will take us the api page where we can generate the api and also see it's analytics and all details.

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/insurance-ai

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRES_IN=7d

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
