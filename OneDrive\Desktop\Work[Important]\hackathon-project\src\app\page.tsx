"use client"

import AiInput from '@/components/ui/ai-input'
import React, { useState, useEffect } from 'react'
import Navigation from '@/components/Navigation/navigation'
import { Globe, Upload, FileText, Loader2, Key, ArrowRight } from 'lucide-react'
import apiService from '@/services/api'

interface Message {
  text: string;
  isAi: boolean;
}

interface ChatHistory {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
}

interface Document {
  id: string;
  filename: string;
  size: number;
  summary?: string;
  keyPoints?: string[];
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

const Page = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isAiTyping, setIsAiTyping] = useState(false)
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | null>(null)
  const [isNavigationOpen, setIsNavigationOpen] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [documents, setDocuments] = useState<Document[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<string>('')

  const generateChatTitle = (messages: Message[]): string => {
    if (messages.length === 0) return "New Chat"
    const firstUserMessage = messages.find(msg => !msg.isAi)
    if (firstUserMessage) {
      return firstUserMessage.text.length > 30
        ? firstUserMessage.text.substring(0, 30) + "..."
        : firstUserMessage.text
    }
    return "New Chat"
  }

  const handleNewChat = () => {
    // Save current chat to history if it has messages
    if (messages.length > 0) {
      const newChat: ChatHistory = {
        id: currentSessionId || Date.now().toString(),
        title: generateChatTitle(messages),
        messages: [...messages],
        timestamp: new Date()
      }
      setChatHistory(prev => [newChat, ...prev])
    }

    // Clear current chat and start new session
    setMessages([])
    setCurrentChatId(null)
    setCurrentSessionId(null)
    setDocuments([])
    setIsAiTyping(false)
  }

  const handleLoadChat = (chat: ChatHistory) => {
    // Save current chat if it has messages and is different from the one being loaded
    if (messages.length > 0 && currentChatId !== chat.id) {
      const currentChat: ChatHistory = {
        id: currentChatId || Date.now().toString(),
        title: generateChatTitle(messages),
        messages: [...messages],
        timestamp: new Date()
      }
      setChatHistory(prev => {
        const filtered = prev.filter(c => c.id !== currentChatId)
        return [currentChat, ...filtered]
      })
    }

    // Load selected chat
    setMessages(chat.messages)
    setCurrentChatId(chat.id)
    setIsAiTyping(false)
  }

  const handleDeleteChat = (chatId: string) => {
    setChatHistory(prev => prev.filter(chat => chat.id !== chatId))

    // If the deleted chat was the current one, clear the current chat
    if (currentChatId === chatId) {
      setMessages([])
      setCurrentChatId(null)
      setIsAiTyping(false)
    }
  }

  const handleMessage = async (message: string) => {
    if (!currentSessionId || documents.length === 0) {
      setMessages(prev => [...prev,
        { text: message, isAi: false },
        { text: "Please upload insurance documents first before asking questions.", isAi: true }
      ])
      return
    }

    // Add user message
    setMessages(prev => [...prev, { text: message, isAi: false }])
    setIsAiTyping(true)

    try {
      const response = await apiService.sendMessage(message, currentSessionId)
      setMessages(prev => [...prev, { text: response.response, isAi: true }])
    } catch (error) {
      console.error('Error sending message:', error)
      setMessages(prev => [...prev, {
        text: "Sorry, I encountered an error while processing your message. Please try again.",
        isAi: true
      }])
    } finally {
      setIsAiTyping(false)
    }
  }

  const handleFileUpload = async (files: FileList) => {
    if (!files || files.length === 0) return

    setIsUploading(true)
    setUploadProgress('Uploading files...')

    try {
      const fileArray = Array.from(files)
      const sessionId = currentSessionId || apiService.generateSessionId()

      if (!currentSessionId) {
        setCurrentSessionId(sessionId)
      }

      const response = await apiService.uploadDocuments(fileArray, sessionId)

      if (response.success) {
        setDocuments(response.documents)
        setUploadProgress('Processing documents...')

        // Generate summaries for each document
        for (const doc of response.documents) {
          try {
            const summaryResponse = await apiService.generateSummary(doc.id)
            if (summaryResponse.success) {
              setDocuments(prev => prev.map(d =>
                d.id === doc.id
                  ? { ...d, summary: summaryResponse.summary, keyPoints: summaryResponse.keyPoints }
                  : d
              ))
            }
          } catch (error) {
            console.error('Error generating summary for document:', doc.filename, error)
          }
        }

        setUploadProgress('')

        // Add a system message about the uploaded documents
        const docList = response.documents.map(doc => doc.filename).join(', ')
        setMessages(prev => [...prev, {
          text: `Successfully uploaded and processed ${response.documents.length} document(s): ${docList}. You can now ask questions about your insurance coverage.`,
          isAi: true
        }])
      }
    } catch (error) {
      console.error('Error uploading files:', error)
      setUploadProgress('')
      setMessages(prev => [...prev, {
        text: "Sorry, there was an error uploading your documents. Please try again.",
        isAi: true
      }])
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className='flex h-screen'>
      {/* Navigation Sidebar */}
      <div className={`transition-all duration-300 ${isNavigationOpen ? 'w-64' : 'w-0'} overflow-hidden`}>
        <Navigation
          isOpen={isNavigationOpen}
          onToggle={() => setIsNavigationOpen(!isNavigationOpen)}
          onNewChat={handleNewChat}
          onLoadChat={handleLoadChat}
          onDeleteChat={handleDeleteChat}
          chatHistory={chatHistory}
          currentChatId={currentChatId}
        />
      </div>

      {/* Main Chat Area */}
      <div className='flex-1 flex flex-col relative'>
        {/* Header with Navigation Toggle and API Button */}
        <div className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-4">
          {!isNavigationOpen && (
            <button
              onClick={() => setIsNavigationOpen(true)}
              className="bg-white p-2 rounded-full shadow-lg hover:shadow-xl transition-shadow"
            >
              <Globe className="w-6 h-6" />
            </button>
          )}
          {isNavigationOpen && <div></div>}

          {/* API Button */}
          <button
            onClick={() => window.open('/api', '_blank')}
            className="bg-white p-2 rounded-full shadow-lg hover:shadow-xl transition-shadow flex items-center gap-2 px-4"
          >
            <Key className="w-5 h-5" />
            <span className="text-sm font-medium">API</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto px-4 pb-32 pt-16">
          <div className="max-w-4xl mx-auto">
            {/* File Upload Area */}
            {documents.length === 0 && !isUploading && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-6">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-2">
                  Upload Insurance Documents
                </h3>
                <p className="text-gray-500 mb-4">
                  Upload your insurance policies (PDF, Word, or images) to get started
                </p>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.tiff,.bmp"
                  onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="inline-flex items-center px-4 py-2 bg-[#ff3f17] text-white rounded-lg hover:bg-[#ff3f17]/90 cursor-pointer"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Files
                </label>
              </div>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <Loader2 className="w-5 h-5 animate-spin text-blue-500 mr-3" />
                  <span className="text-blue-700">{uploadProgress}</span>
                </div>
              </div>
            )}

            {/* Documents List */}
            {documents.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-gray-700 mb-3 flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Uploaded Documents ({documents.length})
                </h4>
                <div className="space-y-2">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between bg-white p-3 rounded border">
                      <div>
                        <span className="font-medium text-gray-800">{doc.filename}</span>
                        <span className="text-sm text-gray-500 ml-2">
                          ({(doc.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs ${
                        doc.processingStatus === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {doc.processingStatus}
                      </span>
                    </div>
                  ))}
                </div>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.tiff,.bmp"
                  onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                  className="hidden"
                  id="additional-files"
                />
                <label
                  htmlFor="additional-files"
                  className="inline-flex items-center px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 cursor-pointer mt-3"
                >
                  <Upload className="w-3 h-3 mr-1" />
                  Add More Files
                </label>
              </div>
            )}

            <div className="flex flex-col gap-4 py-4">
              {messages.map((msg, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg max-w-[80%] ${
                    msg.isAi
                      ? 'bg-neutral-100 self-start'
                      : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'
                  }`}
                >
                  {msg.text}
                </div>
              ))}
              {isAiTyping && (
                <div className="p-3 rounded-lg bg-neutral-100 self-start flex items-center">
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  AI is analyzing your documents...
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Input Area - Centered */}
        <div className="absolute bottom-6 left-0 right-0 px-4">
          <div className="max-w-4xl mx-auto">
            <AiInput onSendMessage={handleMessage} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page