"use client"

import AiInput from '@/components/ui/ai-input'
import React, { useState } from 'react'
import Navigation from '@/components/Navigation/navigation'

interface Message {
  text: string;
  isAi: boolean;
}

interface ChatHistory {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
}

const Page = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isAiTyping, setIsAiTyping] = useState(false)
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | null>(null)

  const generateChatTitle = (messages: Message[]): string => {
    if (messages.length === 0) return "New Chat"
    const firstUserMessage = messages.find(msg => !msg.isAi)
    if (firstUserMessage) {
      return firstUserMessage.text.length > 30
        ? firstUserMessage.text.substring(0, 30) + "..."
        : firstUserMessage.text
    }
    return "New Chat"
  }

  const handleNewChat = () => {
    // Save current chat to history if it has messages
    if (messages.length > 0) {
      const newChat: ChatHistory = {
        id: Date.now().toString(),
        title: generateChatTitle(messages),
        messages: [...messages],
        timestamp: new Date()
      }
      setChatHistory(prev => [newChat, ...prev])
    }

    // Clear current chat
    setMessages([])
    setCurrentChatId(null)
    setIsAiTyping(false)
  }

  const handleLoadChat = (chat: ChatHistory) => {
    // Save current chat if it has messages and is different from the one being loaded
    if (messages.length > 0 && currentChatId !== chat.id) {
      const currentChat: ChatHistory = {
        id: currentChatId || Date.now().toString(),
        title: generateChatTitle(messages),
        messages: [...messages],
        timestamp: new Date()
      }
      setChatHistory(prev => {
        const filtered = prev.filter(c => c.id !== currentChatId)
        return [currentChat, ...filtered]
      })
    }

    // Load selected chat
    setMessages(chat.messages)
    setCurrentChatId(chat.id)
    setIsAiTyping(false)
  }

  const handleMessage = async (message: string) => {
    // Add user message
    setMessages(prev => [...prev, { text: message, isAi: false }])
    setIsAiTyping(true)

    // Simulate AI response (replace this with actual AI integration)
    setTimeout(() => {
      setMessages(prev => [...prev, { text: "This is a simulated AI response.", isAi: true }])
      setIsAiTyping(false)
    }, 1000)
  }

  return (
    <div className='flex'>
      <div className="w-[250px]">
        <Navigation
          onNewChat={handleNewChat}
          onLoadChat={handleLoadChat}
          chatHistory={chatHistory}
          currentChatId={currentChatId}
        />
      </div>
      <div className='flex-1 flex flex-col h-[690px] relative'>
        <div className="absolute inset-0 overflow-y-auto px-4 pb-32">
          <div className="flex flex-col gap-4 py-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg max-w-[80%] ${
                  msg.isAi
                    ? 'bg-neutral-100 self-start'
                    : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'
                }`}
              >
                {msg.text}
              </div>
            ))}
            {isAiTyping && (
              <div className="p-3 rounded-lg bg-neutral-100 self-start">
                AI is typing...
              </div>
            )}
          </div>
        </div>
        <div className="absolute bottom-6 left-0 right-0 px-4">
          <AiInput onSendMessage={handleMessage} />
        </div>
      </div>
    </div>
  )
}

export default Page