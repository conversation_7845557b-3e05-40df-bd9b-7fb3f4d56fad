"use client"

import AiInput from '@/components/ui/ai-input'
import React, { useState } from 'react'
import Navigation from '@/components/Navigation/navigation'

const Page = () => {
  const [messages, setMessages] = useState<Array<{ text: string; isAi: boolean }>>([])
  const [isAiTyping, setIsAiTyping] = useState(false)

  const handleMessage = async (message: string) => {
    // Add user message
    setMessages(prev => [...prev, { text: message, isAi: false }])
    setIsAiTyping(true)

    // Simulate AI response (replace this with actual AI integration)
    setTimeout(() => {
      setMessages(prev => [...prev, { text: "This is a simulated AI response.", isAi: true }])
      setIsAiTyping(false)
    }, 1000)
  }

  return (
    <div className='flex'>
      <div className="w-[250px]">
        <Navigation />
      </div>
      <div className='flex-1 flex flex-col h-[690px] relative'>
        <div className="absolute inset-0 overflow-y-auto px-4 pb-32">
          <div className="flex flex-col gap-4 py-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg max-w-[80%] ${
                  msg.isAi
                    ? 'bg-neutral-100 self-start'
                    : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'
                }`}
              >
                {msg.text}
              </div>
            ))}
            {isAiTyping && (
              <div className="p-3 rounded-lg bg-neutral-100 self-start">
                AI is typing...
              </div>
            )}
          </div>
        </div>
        <div className="absolute bottom-6 left-0 right-0 px-4">
          <AiInput onSendMessage={handleMessage} />
        </div>
      </div>
    </div>
  )
}

export default Page