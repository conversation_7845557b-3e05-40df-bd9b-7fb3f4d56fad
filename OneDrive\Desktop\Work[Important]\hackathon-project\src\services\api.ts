const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Generate a unique user ID for this session
const getUserId = () => {
  // Check if we're in the browser (not SSR)
  if (typeof window === 'undefined') {
    return 'user_' + Math.random().toString(36).substr(2, 9);
  }

  let userId = localStorage.getItem('userId');
  if (!userId) {
    userId = 'user_' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('userId', userId);
  }
  return userId;
};

// Generate a unique session ID for each chat session
const generateSessionId = () => {
  return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
};

class ApiService {
  private baseURL: string;
  private userId: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private getUserId(): string {
    if (!this.userId) {
      this.userId = getUserId();
    }
    return this.userId;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'x-user-id': this.getUserId(),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async makeFormRequest(endpoint: string, formData: FormData, sessionId?: string) {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'x-user-id': this.getUserId(),
    };

    if (sessionId) {
      headers['x-session-id'] = sessionId;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API form request failed:', error);
      throw error;
    }
  }

  // Document upload
  async uploadDocuments(files: File[], sessionId?: string) {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('documents', file);
    });

    return this.makeFormRequest('/documents/upload', formData, sessionId);
  }

  // Get document details
  async getDocument(documentId: string) {
    return this.makeRequest(`/documents/${documentId}`);
  }

  // Get documents for a session
  async getSessionDocuments(sessionId: string) {
    return this.makeRequest(`/documents/session/${sessionId}`);
  }

  // Generate document summary
  async generateSummary(documentId: string) {
    return this.makeRequest(`/chat/summarize/${documentId}`, {
      method: 'POST',
    });
  }

  // Send chat message
  async sendMessage(message: string, sessionId: string) {
    return this.makeRequest('/chat/message', {
      method: 'POST',
      body: JSON.stringify({
        message,
        sessionId,
      }),
    });
  }

  // Get chat history
  async getChatHistory(sessionId: string) {
    return this.makeRequest(`/chat/history/${sessionId}`);
  }

  // Stream chat response (for future implementation)
  async streamMessage(message: string, sessionId: string, onChunk: (chunk: string) => void) {
    const url = `${this.baseURL}/chat/stream`;
    const headers = {
      'Content-Type': 'application/json',
      'x-user-id': this.userId,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          message,
          sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.content) {
                onChunk(data.content);
              }
              if (data.done) {
                return;
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Stream request failed:', error);
      throw error;
    }
  }

  // API Key Management
  async createApiKey(name: string, permissions: string[] = ['read', 'write']) {
    return this.makeRequest('/auth/api-keys', {
      method: 'POST',
      body: JSON.stringify({
        name,
        permissions,
      }),
    });
  }

  async getApiKeys() {
    return this.makeRequest('/auth/api-keys');
  }

  async updateApiKey(keyId: string, updates: any) {
    return this.makeRequest(`/auth/api-keys/${keyId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteApiKey(keyId: string) {
    return this.makeRequest(`/auth/api-keys/${keyId}`, {
      method: 'DELETE',
    });
  }

  async regenerateApiKey(keyId: string) {
    return this.makeRequest(`/auth/api-keys/${keyId}/regenerate`, {
      method: 'POST',
    });
  }

  // Analytics
  async getAnalyticsOverview(timeRange: string = '30d') {
    return this.makeRequest(`/analytics/overview?timeRange=${timeRange}`);
  }

  async getApiKeyAnalytics(keyId: string, timeRange: string = '30d') {
    return this.makeRequest(`/analytics/api-key/${keyId}?timeRange=${timeRange}`);
  }

  async getRecentRequests(limit: number = 50, apiKeyId?: string) {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (apiKeyId) params.append('apiKeyId', apiKeyId);
    return this.makeRequest(`/analytics/recent-requests?${params}`);
  }

  // Local Storage Methods for Files
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  private base64ToFile(base64: string, filename: string, mimeType: string): File {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new File([byteArray], filename, { type: mimeType });
  }

  async storeFilesLocally(files: File[], sessionId: string) {
    if (typeof window === 'undefined') return [];

    const storedFiles = [];

    for (const file of files) {
      try {
        const base64 = await this.fileToBase64(file);
        const fileData = {
          id: 'file_' + Math.random().toString(36).substr(2, 9),
          name: file.name,
          size: file.size,
          type: file.type,
          base64: base64,
          uploadedAt: new Date().toISOString(),
          sessionId: sessionId
        };

        // Store in localStorage
        localStorage.setItem(`file_${fileData.id}`, JSON.stringify(fileData));

        // Add to session file list
        const sessionFiles = this.getSessionFiles(sessionId);
        sessionFiles.push(fileData.id);
        localStorage.setItem(`session_${sessionId}_files`, JSON.stringify(sessionFiles));

        storedFiles.push(fileData);
      } catch (error) {
        console.error('Error storing file locally:', error);
      }
    }

    return storedFiles;
  }

  getSessionFiles(sessionId: string): string[] {
    if (typeof window === 'undefined') return [];

    const stored = localStorage.getItem(`session_${sessionId}_files`);
    return stored ? JSON.parse(stored) : [];
  }

  getStoredFile(fileId: string) {
    if (typeof window === 'undefined') return null;

    const stored = localStorage.getItem(`file_${fileId}`);
    return stored ? JSON.parse(stored) : null;
  }

  getStoredFilesForSession(sessionId: string) {
    const fileIds = this.getSessionFiles(sessionId);
    return fileIds.map(id => this.getStoredFile(id)).filter(Boolean);
  }

  async processStoredFiles(sessionId: string) {
    const storedFiles = this.getStoredFilesForSession(sessionId);

    if (storedFiles.length === 0) {
      throw new Error('No files found in local storage for this session');
    }

    // Convert stored files back to File objects
    const files = storedFiles.map(fileData =>
      this.base64ToFile(fileData.base64, fileData.name, fileData.type)
    );

    // Create a mock response that simulates backend processing
    const processedDocuments = storedFiles.map(fileData => ({
      id: fileData.id,
      filename: fileData.name,
      size: fileData.size,
      wordCount: Math.floor(fileData.size / 6), // Rough estimate
      summary: `This is a ${fileData.type.includes('pdf') ? 'PDF' : 'document'} file named ${fileData.name}. The AI will analyze this document to answer your questions about insurance coverage, terms, and conditions.`,
      keyPoints: [
        'Document uploaded and stored locally',
        'Ready for AI analysis',
        `File size: ${(fileData.size / 1024 / 1024).toFixed(2)} MB`
      ],
      processingStatus: 'completed'
    }));

    return {
      success: true,
      sessionId: sessionId,
      message: `Processed ${processedDocuments.length} documents from local storage`,
      documents: processedDocuments
    };
  }

  // Simulate AI chat using stored files
  async chatWithStoredFiles(message: string, sessionId: string) {
    const storedFiles = this.getStoredFilesForSession(sessionId);

    if (storedFiles.length === 0) {
      throw new Error('No documents found. Please upload documents first.');
    }

    // Get chat history from localStorage
    const chatHistory = this.getChatHistory(sessionId);

    // Return error message for localStorage mode
    const fileNames = storedFiles.map(f => f.name).join(', ');
    const response = `I have your uploaded documents (${fileNames}) stored locally, but I need the Python backend server to be running to provide real AI-powered analysis.

To get actual AI responses:
1. Start the Python backend: cd python-backend && python run.py
2. The backend will analyze your documents using Groq AI
3. You'll get real answers about your insurance coverage

Your question: "${message}"

Please start the backend server for real AI analysis of your insurance documents.`;

    // Store chat message in localStorage
    const newMessage = {
      role: 'user' as const,
      content: message,
      timestamp: new Date().toISOString()
    };

    const aiResponse = {
      role: 'assistant' as const,
      content: response,
      timestamp: new Date().toISOString()
    };

    chatHistory.push(newMessage, aiResponse);
    this.saveChatHistory(sessionId, chatHistory);

    return {
      success: true,
      response: response,
      sessionId: sessionId,
      tokensUsed: 0,
      documentsCount: storedFiles.length
    };
  }

  getChatHistory(sessionId: string) {
    if (typeof window === 'undefined') return [];

    const stored = localStorage.getItem(`chat_${sessionId}`);
    return stored ? JSON.parse(stored) : [];
  }

  saveChatHistory(sessionId: string, messages: any[]) {
    if (typeof window === 'undefined') return;

    localStorage.setItem(`chat_${sessionId}`, JSON.stringify(messages));
  }

  clearSession(sessionId: string) {
    if (typeof window === 'undefined') return;

    // Remove all files for this session
    const fileIds = this.getSessionFiles(sessionId);
    fileIds.forEach(id => {
      localStorage.removeItem(`file_${id}`);
    });

    // Remove session data
    localStorage.removeItem(`session_${sessionId}_files`);
    localStorage.removeItem(`chat_${sessionId}`);
  }

  // Utility methods
  generateSessionId() {
    return generateSessionId();
  }

  getUserId() {
    return this.getUserId();
  }
}

export default new ApiService();
