const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Generate a unique user ID for this session
const getUserId = () => {
  // Check if we're in the browser (not SSR)
  if (typeof window === 'undefined') {
    return 'user_' + Math.random().toString(36).substr(2, 9);
  }

  let userId = localStorage.getItem('userId');
  if (!userId) {
    userId = 'user_' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('userId', userId);
  }
  return userId;
};

// Generate a unique session ID for each chat session
const generateSessionId = () => {
  return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
};

class ApiService {
  private baseURL: string;
  private userId: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private getUserId(): string {
    if (!this.userId) {
      this.userId = getUserId();
    }
    return this.userId;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'x-user-id': this.getUserId(),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async makeFormRequest(endpoint: string, formData: FormData, sessionId?: string) {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'x-user-id': this.getUserId(),
    };

    if (sessionId) {
      headers['x-session-id'] = sessionId;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API form request failed:', error);
      throw error;
    }
  }

  // Document upload
  async uploadDocuments(files: File[], sessionId?: string) {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('documents', file);
    });

    return this.makeFormRequest('/documents/upload', formData, sessionId);
  }

  // Get document details
  async getDocument(documentId: string) {
    return this.makeRequest(`/documents/${documentId}`);
  }

  // Get documents for a session
  async getSessionDocuments(sessionId: string) {
    return this.makeRequest(`/documents/session/${sessionId}`);
  }

  // Generate document summary
  async generateSummary(documentId: string) {
    return this.makeRequest(`/chat/summarize/${documentId}`, {
      method: 'POST',
    });
  }

  // Send chat message
  async sendMessage(message: string, sessionId: string) {
    return this.makeRequest('/chat/message', {
      method: 'POST',
      body: JSON.stringify({
        message,
        sessionId,
      }),
    });
  }

  // Get chat history
  async getChatHistory(sessionId: string) {
    return this.makeRequest(`/chat/history/${sessionId}`);
  }

  // Stream chat response (for future implementation)
  async streamMessage(message: string, sessionId: string, onChunk: (chunk: string) => void) {
    const url = `${this.baseURL}/chat/stream`;
    const headers = {
      'Content-Type': 'application/json',
      'x-user-id': this.userId,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          message,
          sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.content) {
                onChunk(data.content);
              }
              if (data.done) {
                return;
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Stream request failed:', error);
      throw error;
    }
  }

  // API Key Management
  async createApiKey(name: string, permissions: string[] = ['read', 'write']) {
    return this.makeRequest('/auth/api-keys', {
      method: 'POST',
      body: JSON.stringify({
        name,
        permissions,
      }),
    });
  }

  async getApiKeys() {
    return this.makeRequest('/auth/api-keys');
  }

  async updateApiKey(keyId: string, updates: any) {
    return this.makeRequest(`/auth/api-keys/${keyId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteApiKey(keyId: string) {
    return this.makeRequest(`/auth/api-keys/${keyId}`, {
      method: 'DELETE',
    });
  }

  async regenerateApiKey(keyId: string) {
    return this.makeRequest(`/auth/api-keys/${keyId}/regenerate`, {
      method: 'POST',
    });
  }

  // Analytics
  async getAnalyticsOverview(timeRange: string = '30d') {
    return this.makeRequest(`/analytics/overview?timeRange=${timeRange}`);
  }

  async getApiKeyAnalytics(keyId: string, timeRange: string = '30d') {
    return this.makeRequest(`/analytics/api-key/${keyId}?timeRange=${timeRange}`);
  }

  async getRecentRequests(limit: number = 50, apiKeyId?: string) {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (apiKeyId) params.append('apiKeyId', apiKeyId);
    return this.makeRequest(`/analytics/recent-requests?${params}`);
  }

  // Utility methods
  generateSessionId() {
    return generateSessionId();
  }

  getUserId() {
    return this.userId;
  }
}

export default new ApiService();
